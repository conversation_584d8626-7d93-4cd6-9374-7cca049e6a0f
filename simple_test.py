#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Kafka连接测试脚本
快速验证Kafka连接和基本功能
"""

import json
import time
from datetime import datetime
from kafka import KafkaProducer, KafkaConsumer
from kafka.errors import KafkaError

# Kafka配置
KAFKA_SERVERS = ['localhost:9092']  # 修改为您的Kafka服务器地址
TOPICS = [
    'tran_code_queue_low',
    'tran_code_queue_high', 
    'tran_code_query',
    'synthesis_queue',
    'notice_queue',
    'delete_file_queue'
]

def test_kafka_connection():
    """测试Kafka连接"""
    print("🔗 测试Kafka连接...")
    
    try:
        # 创建生产者测试连接
        producer = KafkaProducer(
            bootstrap_servers=KAFKA_SERVERS,
            value_serializer=lambda v: json.dumps(v, ensure_ascii=False).encode('utf-8'),
            api_version=(0, 10, 1)
        )
        
        print("✅ Kafka连接成功!")
        producer.close()
        return True
        
    except Exception as e:
        print(f"❌ Kafka连接失败: {e}")
        return False

def send_test_messages():
    """发送测试消息到所有队列"""
    print("\n📤 发送测试消息...")
    
    try:
        producer = KafkaProducer(
            bootstrap_servers=KAFKA_SERVERS,
            value_serializer=lambda v: json.dumps(v, ensure_ascii=False).encode('utf-8'),
            api_version=(0, 10, 1)
        )
        
        for i, topic in enumerate(TOPICS):
            message = {
                'id': i + 1,
                'topic': topic,
                'message': f'测试消息发送到 {topic}',
                'timestamp': datetime.now().isoformat(),
                'test_data': f'这是第{i+1}个测试消息'
            }
            
            try:
                future = producer.send(topic, value=message)
                record_metadata = future.get(timeout=10)
                
                print(f"✅ 消息发送成功到 '{topic}'")
                print(f"   分区: {record_metadata.partition}, 偏移量: {record_metadata.offset}")
                
            except Exception as e:
                print(f"❌ 发送到 '{topic}' 失败: {e}")
        
        producer.close()
        print("📤 所有测试消息发送完成!")
        
    except Exception as e:
        print(f"❌ 发送消息时出错: {e}")

def consume_test_messages():
    """消费测试消息"""
    print("\n📥 开始消费消息...")
    
    try:
        consumer = KafkaConsumer(
            *TOPICS,  # 订阅所有主题
            bootstrap_servers=KAFKA_SERVERS,
            value_deserializer=lambda m: json.loads(m.decode('utf-8')) if m else None,
            auto_offset_reset='earliest',  # 从最早的消息开始
            group_id='simple_test_group',
            api_version=(0, 10, 1),
            consumer_timeout_ms=10000  # 10秒超时
        )
        
        message_count = 0
        print("🔍 等待消息...")
        
        for message in consumer:
            message_count += 1
            print(f"\n📨 收到消息 #{message_count}:")
            print(f"   主题: {message.topic}")
            print(f"   分区: {message.partition}")
            print(f"   偏移量: {message.offset}")
            print(f"   内容: {message.value}")
            
            # 限制显示消息数量
            if message_count >= 10:
                print("📋 已显示10条消息，停止消费...")
                break
        
        consumer.close()
        
        if message_count == 0:
            print("ℹ️  没有收到任何消息")
        else:
            print(f"✅ 总共消费了 {message_count} 条消息")
            
    except Exception as e:
        print(f"❌ 消费消息时出错: {e}")

def main():
    """主函数"""
    print("🚀 开始Kafka简单测试")
    print("=" * 50)
    
    # 1. 测试连接
    if not test_kafka_connection():
        print("❌ 连接测试失败，请检查Kafka服务器配置")
        return
    
    # 2. 发送测试消息
    send_test_messages()
    
    # 等待一下让消息完全写入
    print("\n⏳ 等待2秒让消息写入...")
    time.sleep(2)
    
    # 3. 消费测试消息
    consume_test_messages()
    
    print("\n🎉 测试完成!")
    print("\n📋 测试的队列:")
    for i, topic in enumerate(TOPICS, 1):
        print(f"   {i}. {topic}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
