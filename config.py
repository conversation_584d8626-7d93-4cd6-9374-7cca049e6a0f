# Kafka配置文件
KAFKA_CONFIG = {
    'bootstrap_servers': ['111.230.107.201:9092'],  # 修改为您的Kafka服务器地址
    'client_id': 'kafka_test_client',
    'api_version': (0, 10, 1),
    'security_protocol': 'PLAINTEXT',  # 无认证
}

# 队列/主题名称
TOPICS = {
    'tran_code_queue_low': 'tran_code_queue_low',
    'tran_code_queue_high': 'tran_code_queue_high', 
    'tran_code_query': 'tran_code_query',
    'synthesis_queue': 'synthesis_queue',
    'notice_queue': 'notice_queue',
    'delete_file_queue': 'delete_file_queue'
}

# 生产者配置
PRODUCER_CONFIG = {
    **KAFKA_CONFIG,
    'value_serializer': lambda v: v.encode('utf-8') if isinstance(v, str) else v,
    'key_serializer': lambda k: k.encode('utf-8') if isinstance(k, str) else k,
    'acks': 'all',  # 等待所有副本确认
    'retries': 3,
    'batch_size': 16384,
    'linger_ms': 10,
}

# 消费者配置
CONSUMER_CONFIG = {
    **KAFKA_CONFIG,
    'value_deserializer': lambda m: m.decode('utf-8') if m else None,
    'key_deserializer': lambda m: m.decode('utf-8') if m else None,
    'auto_offset_reset': 'latest',  # 从最新消息开始消费
    'enable_auto_commit': True,
    'group_id': 'test_consumer_group',
}
