# Kafka测试代码

这是一个用于测试Kafka连接和队列操作的Python脚本。

## 支持的队列/主题

- `tran_code_queue_low` - 低优先级交易代码队列
- `tran_code_queue_high` - 高优先级交易代码队列  
- `tran_code_query` - 交易代码查询队列
- `synthesis_queue` - 数据合成队列
- `notice_queue` - 通知队列
- `delete_file_queue` - 文件删除队列

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置

在 `config.py` 文件中修改Kafka服务器地址：

```python
KAFKA_CONFIG = {
    'bootstrap_servers': ['localhost:9092'],  # 修改为您的Kafka服务器地址
    # ...
}
```

## 运行测试

```bash
python kafka_test.py
```

## 功能特性

1. **自动连接测试** - 测试与Kafka服务器的连接
2. **主题创建** - 自动创建所有需要的主题
3. **消息发送** - 向每个队列发送测试消息
4. **消息消费** - 从每个队列消费并显示消息
5. **错误处理** - 完善的错误处理和状态显示

## 输出示例

```
正在连接到Kafka...
✅ Kafka连接成功!
✅ 主题 'tran_code_queue_low' 创建成功
✅ 主题 'tran_code_queue_high' 创建成功
...
📤 发送测试消息...
✅ 消息发送成功到 'tran_code_queue_low':
   分区: 0
   偏移量: 123
   消息: {'type': 'low_priority_transaction', 'code': 'TXN001', 'amount': 100.5}
...
```

## 自定义使用

您可以修改 `kafka_test.py` 中的测试数据来适应您的具体需求：

```python
test_messages = {
    'tran_code_queue_low': {'your': 'custom_data'},
    # ...
}
```

## 注意事项

- 确保Kafka服务器正在运行
- 默认配置假设Kafka运行在 `localhost:9092`
- 无需用户名密码认证（PLAINTEXT模式）
- 消费者组ID可以在配置中自定义
