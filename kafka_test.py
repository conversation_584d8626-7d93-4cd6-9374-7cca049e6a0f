#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Kafka测试代码 - 连接并测试所有队列
支持生产者和消费者功能
"""

import json
import time
import threading
from datetime import datetime
from kafka import KafkaProducer, KafkaConsumer, KafkaAdminClient
from kafka.admin import NewTopic
from kafka.errors import TopicAlreadyExistsError, KafkaError
from config import KAFKA_CONFIG, TOPICS, PRODUCER_CONFIG, CONSUMER_CONFIG


class KafkaTestClient:
    def __init__(self):
        self.producer = None
        self.consumers = {}
        self.admin_client = None
        self.running = False
        
    def connect(self):
        """连接到Kafka"""
        try:
            print("正在连接到Kafka...")
            
            # 创建管理客户端
            self.admin_client = KafkaAdminClient(**KAFKA_CONFIG)
            
            # 创建生产者
            self.producer = KafkaProducer(**PRODUCER_CONFIG)
            
            print("✅ Kafka连接成功!")
            return True
            
        except Exception as e:
            print(f"❌ Kafka连接失败: {e}")
            return False
    
    def create_topics(self):
        """创建所有主题"""
        try:
            topics_to_create = []
            for topic_name in TOPICS.values():
                topic = NewTopic(
                    name=topic_name,
                    num_partitions=3,  # 3个分区
                    replication_factor=1  # 1个副本
                )
                topics_to_create.append(topic)
            
            # 创建主题
            result = self.admin_client.create_topics(topics_to_create)
            
            for topic, future in result.items():
                try:
                    future.result()
                    print(f"✅ 主题 '{topic}' 创建成功")
                except TopicAlreadyExistsError:
                    print(f"ℹ️  主题 '{topic}' 已存在")
                except Exception as e:
                    print(f"❌ 主题 '{topic}' 创建失败: {e}")
                    
        except Exception as e:
            print(f"❌ 创建主题时出错: {e}")
    
    def send_test_message(self, topic_key, message_data):
        """发送测试消息"""
        try:
            topic_name = TOPICS[topic_key]
            
            # 构造消息
            message = {
                'timestamp': datetime.now().isoformat(),
                'topic': topic_name,
                'data': message_data
            }
            
            # 发送消息
            future = self.producer.send(
                topic_name, 
                value=json.dumps(message, ensure_ascii=False),
                key=f"test_key_{int(time.time())}"
            )
            
            # 等待发送完成
            record_metadata = future.get(timeout=10)
            
            print(f"✅ 消息发送成功到 '{topic_name}':")
            print(f"   分区: {record_metadata.partition}")
            print(f"   偏移量: {record_metadata.offset}")
            print(f"   消息: {message_data}")
            
            return True
            
        except Exception as e:
            print(f"❌ 发送消息到 '{topic_key}' 失败: {e}")
            return False
    
    def create_consumer(self, topic_key, group_id=None):
        """创建消费者"""
        try:
            topic_name = TOPICS[topic_key]
            
            consumer_config = CONSUMER_CONFIG.copy()
            if group_id:
                consumer_config['group_id'] = group_id
            else:
                consumer_config['group_id'] = f"test_group_{topic_key}"
            
            consumer = KafkaConsumer(topic_name, **consumer_config)
            self.consumers[topic_key] = consumer
            
            print(f"✅ 消费者创建成功，订阅主题: '{topic_name}'")
            return consumer
            
        except Exception as e:
            print(f"❌ 创建消费者失败: {e}")
            return None
    
    def consume_messages(self, topic_key, timeout_seconds=10):
        """消费消息"""
        if topic_key not in self.consumers:
            print(f"❌ 主题 '{topic_key}' 没有对应的消费者")
            return
        
        consumer = self.consumers[topic_key]
        topic_name = TOPICS[topic_key]
        
        print(f"🔍 开始消费主题 '{topic_name}' 的消息 (超时: {timeout_seconds}秒)...")
        
        start_time = time.time()
        message_count = 0
        
        try:
            for message in consumer:
                if time.time() - start_time > timeout_seconds:
                    break
                
                message_count += 1
                print(f"📨 收到消息 #{message_count}:")
                print(f"   主题: {message.topic}")
                print(f"   分区: {message.partition}")
                print(f"   偏移量: {message.offset}")
                print(f"   键: {message.key}")
                print(f"   值: {message.value}")
                print(f"   时间戳: {datetime.fromtimestamp(message.timestamp/1000)}")
                print("-" * 50)
                
        except Exception as e:
            print(f"❌ 消费消息时出错: {e}")
        
        if message_count == 0:
            print(f"ℹ️  在 {timeout_seconds} 秒内没有收到新消息")
        else:
            print(f"✅ 总共消费了 {message_count} 条消息")
    
    def test_all_topics(self):
        """测试所有主题"""
        print("\n" + "="*60)
        print("开始测试所有Kafka主题")
        print("="*60)
        
        # 测试数据
        test_messages = {
            'tran_code_queue_low': {'type': 'low_priority_transaction', 'code': 'TXN001', 'amount': 100.50},
            'tran_code_queue_high': {'type': 'high_priority_transaction', 'code': 'TXN002', 'amount': 5000.00},
            'tran_code_query': {'type': 'transaction_query', 'query_id': 'Q001', 'transaction_id': 'TXN001'},
            'synthesis_queue': {'type': 'data_synthesis', 'job_id': 'SYN001', 'data_sources': ['db1', 'db2']},
            'notice_queue': {'type': 'notification', 'user_id': 'USER001', 'message': '交易完成通知'},
            'delete_file_queue': {'type': 'file_deletion', 'file_path': '/tmp/test_file.txt', 'user_id': 'USER001'}
        }
        
        # 1. 发送测试消息到所有主题
        print("\n📤 发送测试消息...")
        for topic_key, message_data in test_messages.items():
            self.send_test_message(topic_key, message_data)
            time.sleep(0.5)  # 短暂延迟
        
        # 2. 为每个主题创建消费者并消费消息
        print("\n📥 创建消费者并消费消息...")
        for topic_key in TOPICS.keys():
            print(f"\n--- 测试主题: {topic_key} ---")
            self.create_consumer(topic_key)
            time.sleep(1)  # 等待消费者准备就绪
            self.consume_messages(topic_key, timeout_seconds=5)
    
    def close(self):
        """关闭连接"""
        try:
            if self.producer:
                self.producer.close()
                print("✅ 生产者已关闭")
            
            for topic_key, consumer in self.consumers.items():
                consumer.close()
                print(f"✅ 消费者 '{topic_key}' 已关闭")
            
            if self.admin_client:
                self.admin_client.close()
                print("✅ 管理客户端已关闭")
                
        except Exception as e:
            print(f"❌ 关闭连接时出错: {e}")


def main():
    """主函数"""
    client = KafkaTestClient()
    
    try:
        # 连接Kafka
        if not client.connect():
            return
        
        # 创建主题
        client.create_topics()
        
        # 等待主题创建完成
        time.sleep(2)
        
        # 测试所有主题
        client.test_all_topics()
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
    finally:
        # 关闭连接
        client.close()
        print("\n🎉 测试完成!")


if __name__ == "__main__":
    main()
